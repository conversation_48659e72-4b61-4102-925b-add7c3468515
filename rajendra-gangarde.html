<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css"
        integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap" rel="stylesheet">
    <title>Rajendra-Gangarde-Profile Card</title>
    <!-- Bootstrap -->
    <link href="assets/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome CSS-->
    <link href="assets/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/one.css" id="style_theme" rel="stylesheet">
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="images/favicon-32x32.png">
    <style>
        body {
            font-family: 'Lato', sans-serif;
            background-color: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            position: relative;
        }

        /* Strips design */
        .strip-design {
            position: absolute;
            width: 100%;
            height: 20px;
            background: linear-gradient(to right, orange 0%, white 50%, green 100%);
        }

        .strip-design.top {
            top: 0;
        }

        .strip-design.bottom {
            bottom: 0;
        }

        .logo {
            position: absolute;
            top: 50px;
            display: block;
            width: 80%;
            /* Adjusted for responsiveness */
            max-width: 280px;
        }

        .profile-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
            width: 90%;
            /* Adjusted for responsiveness */
            max-width: 500px;
            padding: 30px;
            text-align: center;
            margin-top: 120px;
            position: relative;
            overflow: hidden;
            /* To contain the strip designs */
        }

        /* Add gradient strips to the card */
        .profile-card::before,
        .profile-card::after {
            content: '';
            position: absolute;
            width: 120%;
            height: 15px;
            background: linear-gradient(to right, orange, white, green);
        }

        .profile-card::before {
            top: 0;
            left: -10%;
        }

        .profile-card::after {
            bottom: 0;
            right: -10%;
        }

        .profile-card img.profile-pic {
            border-radius: 50%;
            width: 120px;
            height: 120px;
            object-fit: cover;
            box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
        }

        .profile-card h2 {
            margin: 20px 0 10px;
            font-size: 26px;
            font-weight: 700;
            /* text-transform: uppercase; */
            color: #333;
        }

        .profile-card h4 {
            margin: 0;
            font-size: 18px;
            font-weight: 700;
            text-transform: uppercase;
            color: #555;
        }

        .profile-card .contact-btn {
            margin: 20px 0;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            width: 50%;
            text-transform: uppercase;
            font-weight: 700;
            font-size: 16px;
        }

        .profile-card .info {
            text-align: left;
            margin-top: 25px;
        }

        .profile-card .info p {
            margin: 10px 0;
            color: #333;
            font-size: 16px;
            font-weight: 700;
        }

        .profile-card .info a {
            text-decoration: none;
            color: #007bff;
            font-weight: 700;
        }

        .profile-card .info p i {
            margin-right: 10px;
            color: #007bff;
        }

        .social-icons {
            margin-top: 20px;
        }

        .social-icons a {
            margin: 0 8px;
            color: #333;
            text-decoration: none;
            font-size: 24px;
            position: relative;
        }

        .social-icons a:hover {
            color: #007bff;
        }

        /* Tooltip for social icons - top position */
        .social-icons a[data-tooltip]::before {
            content: attr(data-tooltip);
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: #fff;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            white-space: nowrap;
            display: none;
        }

        .social-icons a:hover[data-tooltip]::before {
            display: block;
        }

        /* Triangle tooltip arrow */
        .social-icons a[data-tooltip]::after {
            content: '';
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            border-width: 5px;
            border-style: solid;
            border-color: transparent transparent #333 transparent;
            display: none;
        }

        .social-icons a:hover[data-tooltip]::after {
            display: block;
        }

        /* Pop-up Styles */
        .popup {
            display: none;
            /* Hidden by default */
            position: fixed;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            justify-content: center;
            align-items: center;
            z-index: 999;
        }

        .carousel {
            position: relative;
            max-width: 80%;
            max-height: 80%;
        }

        .carousel img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 10px;
        }

        .carousel .prev,
        .carousel .next {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px;
            cursor: pointer;
            z-index: 1;
        }

        .carousel .prev {
            left: 10px;
        }

        .carousel .next {
            right: 10px;
        }

        .popup.active {
            display: flex;
            /* Show popup */
        }

        .close-popup {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 30px;
            color: #fff;
            cursor: pointer;
        }

        /* Responsive Styles */
        @media (max-width: 600px) {
            .logo {
                width: 90%;
                /* Full width for mobile */
            }

            .profile-card {
                padding: 20px;
                /* Reduced padding for smaller screens */
            }

            .profile-card h2 {
                font-size: 24px;
                /* Smaller font size */
            }

            .profile-card h4 {
                font-size: 16px;
                /* Smaller font size */
            }

            .profile-card .contact-btn {
                width: 100%;
                /* Full width button */
                font-size: 14px;
                /* Smaller font size */
            }

            .social-icons a {
                font-size: 20px;
                /* Smaller icon size */
            }

            .profile-card .info p {
                font-size: 14px;
                /* Smaller info text */
            }
        }

        .address {
            display: flex;
            /* Use flexbox for alignment */
            align-items: center;
            /* Center the items vertically */
            font-size: 16px;
            /* Set a font size */
            color: #333;
            /* Set a color for the text */
            margin: 10px 0;
            /* Add margin for spacing */
        }

        .address i {
            margin-right: 10px;
            /* Space between the icon and the text */
            color: #007bff;
            /* Set color for the icon */
        }
    </style>
</head>

<body>

    <!-- Top strip design -->
    <div class="strip-design top"></div>

    <img src="./images/logo-one.png" alt="Company Logo" class="logo" style="margin-top: 70px;">

    <div class="profile-card">
        <img src="./images/rajendra-gangarde.jpg" alt="Profile Picture" class="profile-pic">
        <h2>Rajendra Gangarde</h2>
        <h4>MD & CEO</h4>

        <button class="contact-btn" onclick="togglePopup()">Visiting Card</button>

        <div class="info">
            <p>
                <i class="fa-regular fa-envelope"></i>
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
            <p>
                <i class="fa-solid fa-phone"></i>
                <a href="tel:+************">+91 84467 72100</a>
            </p>
            <p class="address"><i class="fa-solid fa-location-dot"></i>&nbsp; Akanksha Apartment, Pallod Farms, Baner, Pune-411045 ,
                Maharashtra, India</p>
            <p>
                <i class="fa-solid fa-globe"></i>
                <a href="https://techvishwaguru.com" target="_blank">&nbsp;techvishwaguru.com</a>
            </p>
        </div>

        <div class="social-icons">
            <a href="https://www.linkedin.com/in/rajendra-gangarde-28b98b50/" target="_blank" data-tooltip="LinkedIn"><i
                    class="fa-brands fa-linkedin" style="color:#0077B5"></i></a>
            <a href="https://www.facebook.com/share/eEuax8qug1KngwB6/?mibextid=qi2Omg" target="_blank"
                data-tooltip="Facebook"><i class="fa-brands fa-facebook" style="color:#4267B2"></i></a>
            <a href="https://www.instagram.com/rajegangarde?igsh=MXNuNmhrcDJlNTJwZw==" target="_blank" data-tooltip="Instagram"><i
                    class="fa-brands fa-instagram" style="color:#C13584"></i></a>
        </div>
    </div>

    <!-- Pop-up for Profile Card -->
    <div class="popup" id="profile-popup">
        <div class="close-popup" onclick="togglePopup()">&times;</div>
        <div class="carousel">
            <img src="./images/rajendra-gangarde.jpg" alt="Profile Picture" id="carousel-image">
            <button class="prev" onclick="changeImage(-1)">&#10094;</button>
            <button class="next" onclick="changeImage(1)">&#10095;</button>
        </div>
    </div>

    <script>
        let currentIndex = 0;
        const images = [
            './images/visiting_card-Rajendra-Gangarde-1.jpg',
            './images/visiting_card-Rajendra-Gangarde-2.jpg', // Add the second image path here
        ];

        function togglePopup() {
            const popup = document.getElementById('profile-popup');
            popup.classList.toggle('active');
            if (popup.classList.contains('active')) {
                updateImage();
            }
        }

        function changeImage(direction) {
            currentIndex += direction;
            if (currentIndex < 0) {
                currentIndex = images.length - 1; // Loop to last image
            } else if (currentIndex >= images.length) {
                currentIndex = 0; // Loop to first image
            }
            updateImage();
        }

        function updateImage() {
            const carouselImage = document.getElementById('carousel-image');
            carouselImage.src = images[currentIndex];
        }
    </script>

    <!-- Bottom strip design -->
    <div class="strip-design bottom"></div>

</body>

</html>