





  @import url('https://fonts.googleapis.com/css?family=Roboto');

  html,body{
    font-family: 'Roboto', sans-serif;
    height: 100%;
  }
  * {
    margin: 0;
    padding: 0;
  }
  i {
    margin-right: 10px;
  }
  /*----------bootstrap-navbar-css------------*/
  .navbar-logo{
    padding: 15px;
    color: #fff;
  }
  .navbar-mainbg{
    background-color: #5161ce;
    padding: 0px;
  }
  #navbarSupportedContent{
    overflow: hidden;
    position: relative;
  }
  #navbarSupportedContent ul{
    padding: 0px;
    margin: 0px;
  }
  #navbarSupportedContent ul li a i{
    margin-right: 10px;
  }
  #navbarSupportedContent li {
    list-style-type: none;
    float: left;
  }




  














  
  /*** Table Styles **/
  
  .table-fill {
    background: white;
    border-radius:3px;
    border-collapse: collapse;
    height: 320px;
    margin: auto;
   
    padding:5px;
    width: 100%;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    animation: float 5s infinite;
   
  }
   
  th {
    color:#D5DDE5;;
    background:#5161ce;
    border-bottom:4px solid #9ea7af;
    border-right: 1px solid #343a45;
    font-size:23px;
    font-weight: 100;
    padding:24px;
    text-align:left;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    vertical-align:middle;
  }
  
  th:first-child {
    border-top-left-radius:3px;
  }
   
  th:last-child {
    border-top-right-radius:3px;
    border-right:none;
  }
    
  tr {
    border-top: 1px solid #C1C3D1;
    border-bottom: 1px solid #C1C3D1;
    color:#666B85;
    font-size:16px;
    font-weight:normal;
    text-shadow: 0 1px 1px rgba(256, 256, 256, 0.1);
  }
   
  tr:hover td {
    background:#4E5066;
    color:#FFFFFF;
    border-top: 1px solid #22262e;
  }
   
  tr:first-child {
    border-top:none;
  }
  
  tr:last-child {
    border-bottom:none;
  }
   
  tr:nth-child(odd) td {
    background:#EBEBEB;
  }
   
  tr:nth-child(odd):hover td {
    background:#4E5066;
  }
  
  tr:last-child td:first-child {
    border-bottom-left-radius:3px;
  }
   
  tr:last-child td:last-child {
    border-bottom-right-radius:3px;
  }
   
  td {
    background:#FFFFFF;
    padding:20px;
    text-align:left;
    vertical-align:middle;
    font-weight:400;
    font-size:18px;
    text-shadow: -1px -1px 1px rgba(0, 0, 0, 0.1);
    border-right: 1px solid #C1C3D1;
  }
  
  td:last-child {
    border-right: 0px;
  }
  
  th.text-left {
    text-align: left;
  }
  
  th.text-center {
    text-align: center;
  }
  
  th.text-right {
    text-align: right;
  }
  
  td.text-left {
    text-align: left;
  }
  
  td.text-center {
    text-align: center;
  }
  td a{
    color: #5161ce;
  }
  td a:hover{
 color: white;
  }
  td.text-right {
    text-align: right;
  } */
  


  /* footer */
  footer ul {
    margin: 0px;
    padding: 0px;
   
}
.footer-section {
  background: #5161ce;
  position: relative;
}


.copyright-area{
  background: #5161ce;
  padding: 25px 0;
}
.copyright-text p {
  margin: 0;
  font-size: 14px;
  color: #ffffff;
}
.copyright-text p a{
  color: #f4f4f4;
}
.footer-menu li {
  display: inline-block;
  margin-left: 20px;
}
.footer-menu li:hover a{
  color: #f3f3f3;
}
.footer-menu li a {
  font-size: 14px;
  color: #ffffff;
}

@media (max-width: 991px){
  .titleh4{
  margin-left: 1%;
  font-size: 10px;
  }
  .titledivp{
    font-size: 7px;
  }
.tablescroll{
 
overflow-x: scroll;
margin-bottom: 0px;
  }
}
.svgimage{
  min-height: 100%;
  margin: 0 auto -50px;
}
