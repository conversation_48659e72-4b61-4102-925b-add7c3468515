<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
<title>Vishwaguru Infotech</title>
<!-- Bootstrap -->
<link href="assets/bootstrap/css/bootstrap.min.css" rel="stylesheet">
<!-- Font Awesome CSS-->
<link href="assets/font-awesome/css/font-awesome.min.css" rel="stylesheet">
<!-- Custom CSS -->
<link href="css/style.css" rel="stylesheet">
<link href="css/one.css" id="style_theme" rel="stylesheet">
<!-- Favicon -->
<link rel="icon" type="image/png" sizes="32x32" href="images/favicon-32x32.png">
</head>
<body>


<!-- Header Start -->
<div id="header"></div>
<!-- Header End --> 
<!-- progress bar -->
<div class="progress-bar-container">
	<div class="progress-bar"></div>
  </div>
<!-- Banner Wrapper Start -->
<div class="banner-wrapper inner-banner">
  <div class="top-banner" style="background-image: url(images/inner-banner5.jpg);background-position: center center;">
    <div class="container">
      <!--<h1>Services</h1>--><br><br>
    </div>
  </div>
  <span id="bottom_row" class="bottom_row"><i class="fa fa-angle-down" aria-hidden="true"></i></span> </div>
<!-- Banner Wrapper End --> 

<!-- Inner Wrapper Start -->
<section class="about-us web-search">
  <div class="container">
    <div class="title">
      <h1 style="text-align:left;font-size:40px;">HRMS &amp; Payroll</h1>
    </div>
    <div class="row">
      <div class="col-sm-6">
        <p>We, at Vishwaguru Infotech, develop very highly advanced, secure, robust Human Resource Management Systems (HRMS) or Human Resource Information Systems (HRIS). HR Technology is an intersection between HRM and Information Technology through the smart HR software. It merges HRM as a discipline and in particular it’s basic HR activities and processes with the information technology.
		Functions of HRMS involve tracking employee records, skills, abilities, salaries, and activities. HRMS systems can distribute information management responsibilities, so that the majority of information gathering is not assigned strictly to HR. It allows employees to update personal information and perform other tasks too, information is always kept more secure, accurate and HR professionals are not burderned with these additional tasks that can be carried out easily by users.With the new threats are emerging everyday, security is of great concern when it comes to selecting a HRMS. The information stored in a HRMS is highly confidential, including organization’s proprietary data and personal information of its employees. It is essential for organization to select a solution that use a method of secure transmission such as SSL which encrypts the data as it transmits over the internet.</p>
	</div>
      <div class="col-sm-6"> <img src="images/testimonials-img3.png" alt=" vishwaguru "/> </div>
    </div>
	<p>Selecting and implementing the appropriate HRMS for your organization can make a huge difference to growth and success of your organization. Though it is possible to do HR functions manually, an automated, robust and secure system can help to increase the productivity levels and can change the way your organization is perceived in the highly technological advanced marketplace.
	<br>HRMS can help in below assistance.
	<br>•	Payroll Management
	<br>•	Recruitment and onboarding
	<br>•	Attendance Records
	<br>•	Performance Evaluation
	<br>•	Employee Scheduling
	<br>•	Employee Self Service
	<br><br><b>Payroll</b><br>
		We have developed very secure, robust, simple Payroll management system for one of our esteemed goverment organization in India. Organization especially government’s system is a very complex system involving many rules and procedures.  The HRMS system takes care of the two main functions Service Records maintenance and Payroll generation of the employees. 
		The Payroll module automates the pay process by gathering data on employee time and attendance, calculating various deductions and taxes, and generating periodic pay bills and annexure reports. In Government, the pay bills are then submitted to Treasury/Accounts department for payments.
		<br><b>Objectives of Payroll Management</b>
		<br>•	Error free generation of pay bill and electronic transfer of pay bill to treasury
		<br>•	To derive cadre management information for planning by the heads of departments/Ministries
		<br>•	Data processing and projection/ budgeting of employee related salary expenses
		<br>•	To get a consistent and unified picture of the employee data
		<br>•	To maintain Service Registers of all employees in electronic form
		<br>•	To improve the productivity and efficiency of your organization
		<br><br>The SR (Service Record) or Personnel Details Module is a component covering all other HR aspects from recording the details of new employee joining service to his /her retirement
		<br>•	The system records basic
		<br>•	Demographic and address data,
		<br>•	Training and development,
		<br>•	Capabilities and skills management records and
		<br>•	Other related activities viz (leave, transfer, promotion etc).

</p>
  </div>
</section>
<!-- Inner Wrapper End --> 

<!-- Footer Wrapper Start -->
<div id="footer"></div>
<!-- jQuery (necessary for Bootstrap's JavaScript plugins) --> 
<script src="assets/jquery/jquery-3.1.1.min.js"></script> 
<!-- Include all compiled plugins (below), or include individual files as needed --> 
<script src="assets/bootstrap/js/bootstrap.min.js"></script> 
<script src="assets/jquery/plugins.js"></script> 
<script src="js/custom.js.js"></script>

</body>
</html>