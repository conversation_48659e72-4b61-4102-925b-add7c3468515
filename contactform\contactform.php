<?php
date_default_timezone_set('Asia/Kolkata');
// Check for empty fields
if(empty($_POST['name'])      ||
   empty($_POST['email'])     ||  
   empty($_POST['phone'])   ||
   !filter_var($_POST['email'],FILTER_VALIDATE_EMAIL))
   {
   echo "No arguments Provided!";
   return false;
   }
   
$name = strip_tags(htmlspecialchars($_POST['name']));
$email_address = strip_tags(htmlspecialchars($_POST['email']));
$phone = strip_tags(htmlspecialchars($_POST['phone']));
$company = strip_tags(htmlspecialchars($_POST['company']));
   
if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
	$ip = $_SERVER['HTTP_CLIENT_IP'];
	} elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
	$ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
	} else {
	$ip = $_SERVER['REMOTE_ADDR'];
	}
	
	foreach($_POST as $key =>$val)
	{
		$data[$key] = addslashes($val);
	}
	
	$body = '<table width="800" border="1" cellpadding="0" cellspacing="0" bordercolor="#2B2D6A">
  <tr>
    <td width="371" style="padding:5px"><table width="835" border="0" cellspacing="5" cellpadding="5" bgcolor="#F5F5F5">
        <tr>
          <td colspan="2" style="color:#263C78;font-size: 18px;font-weight: normal;margin-bottom: 3px;">GoT A Query?</td>
        </tr>
		<tr>
          <td width="210" align="left">IP: </td>
          <td width="625" height="30">'.$ip.'</td>
        </tr>
        <tr>
          <td width="210" align="left">Name: </td>
          <td width="625" height="30">'.$data['name'].'</td>
        </tr>
        <tr>
          <td align="left">E-Mail ID: </td>
          <td height="30">'.$data['email'].'</td>
        </tr>	
		<tr>
          <td align="left">Message: </td>
          <td height="30">'.$data['message'].'</td>
        </tr>	
		<tr>
          <td align="left">Phone: </td>
          <td height="30">'.$data['company'].'</td>
        </tr>		
        <tr>
          <td align="left">&nbsp;</td>
          <td height="30">&nbsp;</td>
        </tr>
        <tr>
          <td height="30"><p>Regards,</p>
            <p>VishwaGuru Team</p></td>
          <td height="30">&nbsp;</td>
        </tr>
      </table></td>
  </tr>
</table>';
	// Write the data in TXT file /////
	//$new_filename = date('d-m-Y').'.txt'; 
	//$filename = 'contact_txt_files/'.$new_filename;
	$filename = 'contact-form-data.txt';
	
	$txt = '';
	$myfile = fopen($filename, "a") or die("Unable to open file!");
	$txt = "Date: ".date('d-m-Y')."\r\n";
	$txt .= "Name: ".$data['name']."\r\n";
	$txt .= "Mail ID: ".$data['email']."\r\n";
	$txt .= "Message: ".$data['message']."\r\n";
	$txt .= "Phone: ".$data['phone']."\r\n";		
	$txt .= "IP: ".$ip."\r\n";	
	$txt .= "============================================================================\r\n";
	
	fwrite($myfile, $txt);	
	fclose($myfile);		
	//// End write TXT file /////
				
	$subject = "Contact Us Form Fill";	
	$to = '<EMAIL>';	
	
	
	// message
	$message = $body;
	
	// To send HTML mail, the Content-type header must be set
	$headers  = 'MIME-Version: 1.0' . "\r\n";
	$headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
	
	// Additional headers
	//$headers .= 'To: Gallop <<EMAIL>>' . "\r\n";
	$headers .= 'From: VishwaGuru Team <<EMAIL>>' . "\r\n";	
	// Mail it
	$mail_status = mail($to, $subject, $message, $headers);
	
	
	echo 'OK';
?>
