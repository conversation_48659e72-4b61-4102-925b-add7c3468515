/*
    Template Name    : HostWay
	Description		 : Responsive HTML5 Hosting and Multipurpose Template.
    Version          : 1.0

==================================================
Table of Content
==================================================

1. Fonts - <PERSON>leway, Lato and Opensans
2. Common CSS
3. Loader CSS
4. Header
5. Top Wrapper
6. Logo Wrapper
7. Navigation
8. Banner Wrapper
9. Header Bottom Arrow
10. Domain Search
11. Domain Prices
12. About Us
13. Web Search
14. Dream Host
15. Our Services
16. What we do
17. Hosting Platforms
18. Testimonials
19. Inner Page Styles
20. Hosting
21. Hosting Features
22. Faq
23. Call Back
24. Domain Register
25. Domain Transfer
26. Domain Price
27. About Us Page
28. Testimonials Page
29. Our Team
30. Login / Sign Up
31. 404
32. Blog Page
33. Blog Details
34. Contact Us Page
35. Contact Details
36. Footer Wrapper
37. Footer
38. Media Quries
39. HRMS
40.New Header

/*
================================================
1. Fonts - Raleway, Lato and Opensans
================================================
*/
@import url('https://fonts.googleapis.com/css?family=Lato:100,100i,300,300i,400,400i,700,700i,900,900i|Open+Sans:300,300i,400,400i,600,600i,700,700i,800,800i|Roboto:100,100i,300,300i,400,400i,500,500i,700,700i,900,900i');
@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap');
/*font-family: 'Lato', sans-serif;
font-family: 'Roboto', sans-serif;
font-family: 'Open Sans', sans-serif;
*/

/*
================================================
2. Common CSS
================================================
*/
* {
	margin: 0;
	padding: 0;
}

body {
	font-family: "Lato", sans-serif;
	font-size: 15px;
	line-height: 24px;
	font-weight: 400;
	color: #2a2a2a;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	margin-top: 0;
	font-family: "Lato", sans-serif;
}

a {
	color: #7f7f7f;
	transition: all 0.3s ease 0s;
}

p {
	margin: 0 0 20px;
}

a:hover {
	color: #000000;
	text-decoration: none;
}

a,
a:hover,
a:active,
a:focus {
	outline: none;
	text-decoration: none;
}

.no-padding {
	padding: 0 !important;
}

.no-margin {
	margin: 0 !important;
}

.btn,
.btn * {
	transition: all 0.3s ease 0s;
}

.offset-top-30 {
	margin-top: 30px;
}

.offset-top-60 {
	margin-top: 60px;
}

.center {
	text-align: center;
}

section {
	width: 100%;
	float: left;
	padding: 40px 0 20px;
}

section .title {
	margin: 0 0 50px 0;
	text-align: center;
}

section h2 {
	font-size: 40px;
	font-weight: 600;
	line-height: 40px;
	color: #25272b;
	text-transform: uppercase;
}

@media (min-width:768px) {
	section h2:before {
		background: rgba(0, 0, 0, 0) url(../images/h2-before.png) no-repeat left top;
		content: "";
		display: inline-block;
		height: 15px;
		margin-right: 30px;
		vertical-align: middle;
		width: 70px;
	}

	section h2:after {
		background: rgba(0, 0, 0, 0) url(../images/h2-after.png) no-repeat left top;
		content: "";
		display: inline-block;
		height: 10px;
		margin-left: 30px;
		vertical-align: middle;
		width: 70px;
	}

	section.what-we-do .title h2:before {
		background: rgba(0, 0, 0, 0) url(../images/h2-before-white.png) no-repeat left top;
	}

	section.what-we-do .title h2:after {
		background: rgba(0, 0, 0, 0) url(../images/h2-after-white.png) no-repeat left top;
	}
}

section .title p {
	display: block;
	font-size: 16px;
	font-weight: 300;
	margin: 0 0 10px 0;
}

section .title p.top {
	display: block;
	font-size: 26px;
	font-weight: 300;
	margin: 0 0 10px;
	text-transform: uppercase;
}

/*color changer*/
#switcher {
	position: fixed;
	bottom: 25%;
	right: -170px;
	width: 170px;
	z-index: 9999;
	font-size: 15px;
	padding-bottom: 20px;
	-webkit-transition: all 0.6s ease;
	transition: all 0.6s ease;
	height: 84px;
	background: #fff;
	box-shadow: 0px 3px 10px 0 rgba(0, 0, 0, 0.2);
}

#switcher.active {
	right: 0px;
}

#switcher p {
	color: #fff;
	bottom: -6px;
	position: absolute;
	left: 21px;
	font-size: 13px;
}

#switcher .theme-click {
	color: #ffffff;
	cursor: pointer;
	display: block;
	font-size: 20px;
	height: 42px;
	line-height: 42px;
	position: absolute;
	left: -42px;
	text-align: center;
	top: 0;
	width: 42px;
	z-index: 2;
	box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.2);
	background: #fd4326;
}

#switcher .theme-color {
	width: 30px;
	height: 30px;
	margin: 6px 10px;
	position: absolute;
	left: 0px;
	cursor: pointer;
}

#switcher .theme-active {
	background: #fd4326;
}

#switcher .theme-color-two {
	background: #288feb;
	left: 40px;
}

#switcher .theme-color-three {
	background: #00b159;
	left: 80px;
}

#switcher .theme-color-four {
	background: #00adef;
	left: 120px;
}

#switcher .theme-color-five {
	background: #20b2aa;
	top: 40px;
	left: 0px;
}

#switcher .theme-color-six {
	background: #ff7701;
	top: 40px;
	left: 40px;
}

#switcher .theme-color-seven {
	background: #756de7;
	top: 40px;
	left: 80px;
}

#switcher .theme-color-eight {
	background: #d74c4c;
	top: 40px;
	left: 120px;
}

/*
================================================
3. Loader CSS
================================================
*/
#dvLoading {
	background: url(../images/loader.gif) no-repeat center center #fff;
	width: 100%;
	height: 100%;
	position: fixed;
	z-index: 9999999;
}

/*
================================================
4. Header
================================================
*/
#myDiv {
	display: block;

	transition: transform 0.4s ease;
}

.hidden {
	transform: translateY(-100%);
}

header {
	width: 100%;
	z-index: 99;
}

header.affix.fadeInDown {
	animation-delay: 0s;
	animation-direction: normal;
	animation-duration: 0.5s;
	animation-fill-mode: forwards;
	animation-iteration-count: 1;
	animation-name: fadeInDown !important;
	animation-play-state: running;
	animation-timing-function: ease;
}

header .fadeInDown {
	animation-name: none !important;
}

header.affix .top-wrapper {
	display: none;
}

@-webkit-keyframes fadeInDown {
	from {
		opacity: 0;
		-webkit-transform: translate3d(0, -100%, 0);
		transform: translate3d(0, -100%, 0);
	}

	to {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}

@keyframes fadeInDown {
	from {
		opacity: 0;
		-webkit-transform: translate3d(0, -100%, 0);
		transform: translate3d(0, -100%, 0);
	}

	to {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}


/*
================================================
5. Top Wrapper
================================================
*/
.top-wrapper {
	padding: 10px 0 12px;
	width: 100%;
	float: left;
	background: #25272b;
}

.top-wrapper ul {
	margin: 0;
}

.top-wrapper ul li {
	display: inline-block;
	color: #8d8d8d;
	padding: 0 0 0 35px;
}

.top-wrapper ul li a:hover {
	color: #fff;
}

.top-wrapper ul li i {
	padding: 0 6px 0 0;
}

.top-wrapper .header-social-icons li {
	list-style: none;
	display: inline-block;
	padding: 0 10px 0 0;
}

.top-wrapper .header-social-icons li i {
	font-size: 14px;
	color: #8d8d8d;
	padding: 0;
}

.top-wrapper .header-social-icons li:hover i {
	opacity: 0.7;
}

/*
================================================
6. Logo Wrapper
================================================
*/
.logo-wrapper {
	padding: 20px 0 21px;
	width: 100%;
	float: left;
	background: #f3f3f3;
}

.affix .logo-wrapper {
	padding: 8px 0 9px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.118);
}

.affix .navbar-nav>li>.dropdown-menu {
	margin: 26px 0 0;
}

/*
================================================
7. Navigation
================================================
*/
.navbar-default {
	background-color: inherit;
	border-color: inherit;
}

.navbar-default .navbar-collapse {
	float: left;
}

.navbar {
	border-radius: 0;
	min-height: auto;
	margin: 18px 0 0 0;
	border: none;
}

.navbar-collapse {
	padding: 0;
}

.navbar-brand {
	padding: 11px 0 0;
	height: auto;
	display: none;
}

.navbar-default .navbar-nav>li>a {
	color: #25272b;
	font-size: 13px;
	text-transform: uppercase;
}

.navbar-default .navbar-nav>li>a>i {
	position: absolute;
	left: 50%;
	bottom: -14px;
}

.navbar-nav>li {
	border-right: 1px solid #b5b5b7;
}

.navbar-nav>li>a {
	padding: 0 20px;
}

.navbar-default .navbar-nav.nav li:hover>a,
.navbar-default .navbar-nav li.active>a:hover,
.navbar-default .navbar-nav li.active>a:focus,
.navbar-default .navbar-nav li.active>a,
.navbar-default .navbar-nav>li>a:focus,
.navbar-default .navbar-nav>li>a:hover,
.navbar-default .nav .open>a,
.navbar-default .nav .open>a:focus,
.navbar-default .nav .open>a:hover,
.side-nav li>a:focus {
	background-color: inherit;
}

.dropdown-menu>li>a {
	color: #000;
	margin: 0;
	padding: 10px 20px;
	font-size: 14px;
	font-weight: 400;
	display: block;
	text-transform: capitalize;
}

.navbar-nav>li>.dropdown-menu {
	top: 100%;
	padding: 0;
	margin: 38px 0 0 0;
	box-shadow: none;
	font-size: 16px;
	text-transform: uppercase;
	border-radius: 0;
	border-top: none;
}

.navbar-nav>li>.dropdown-menu li {
	border-bottom: solid 1px #d7d7d7;
}

.navbar-nav>li>.dropdown-menu li:last-child {
	border-bottom: none;
}

.dropdown-menu>li>a:focus,
.dropdown-menu>li>a:hover {
	text-decoration: none;
	background: #eaeaea !important;
	color: #000 !important;
}

/* Car and Sign Up */
.cart-signup {
	margin: -11px 0 0 20px;
	float: left;
}

.cart-signup li {
	list-style: none;
	display: inline-block;
	vertical-align: middle;
}

.cart-signup li .cart i {
	padding: 0 5px 0 0;
}

.cart-signup li .sign-up {
	padding: 8px 22px;
	margin: 0 0 0 20px;
	border-radius: 6px;
	color: #fff;
	text-transform: uppercase;
}

.cart-signup li .sign-up:hover,
.cart-signup li .sign-up:focus {
	background: #25272b;
	color: #fff;
}

/*
================================================
8. Banner Wrapper
================================================
*/
.banner-wrapper {
	width: 100%;
	float: left;
	position: relative;
}

.banner-wrapper .fade-carousel {
	position: relative;
}

.banner-wrapper .fade-carousel .carousel-control {
	opacity: 1;
	width: 10%;
}

.banner-wrapper .carousel-control.left {
	background-image: none;
}

.banner-wrapper .carousel-control.right {
	background-image: none;
}

.banner-wrapper .fade-carousel .slide-arrows .carousel-control span {
	height: 40px;
	line-height: 35px;
	width: 40px;
	border-radius: 50%;
	border: solid 2px #fafafa;
	margin: 0;
	padding: 0;
	font-size: 20px;
	text-align: center;
}

.banner-wrapper .hero {
	position: absolute;
	top: 30%;
	left: 20%;
	color: #fff;
	text-transform: uppercase;
	opacity: 0;
	width: 70%;
}

.banner-wrapper .carousel .item.active .hero {
	opacity: 1;
}

.banner-wrapper .hero h1 {
	font-size: 110px;
	font-weight: 900;
	margin: 0;
	padding: 0;
	text-transform: uppercase;
	font-family: 'Lato', sans-serif;
}

.banner-wrapper .hero h1 span {
	color: #f3f3f3;
	font-weight: 400;
}

.banner-wrapper .hero h2 {
	font-weight: 100;
	font-size: 44px;
	text-transform: capitalize;
	margin: 0 0 30px 0;
	line-height: 52px;
	font-family: 'Lato', sans-serif;
}

.banner-wrapper .hero h2 span {
	display: block;
}

.banner-wrapper .btn-hero {
	border: solid 1px #e5e5e5;
	border-radius: 3px;
	color: #fff;
	background: none;
	font-size: 12px;
	text-transform: uppercase;
	font-weight: 400;
	margin: 0 10px 0 0;
	padding: 8px 22px;
}

.banner-wrapper .btn-hero:hover {
	color: #fff;
}

/* Slides backgrounds */
.banner-wrapper .fade-carousel .carousel-inner .item {
	transition-property: opacity;
}

.banner-wrapper .fade-carousel .carousel-inner .item,
.banner-wrapper .fade-carousel .carousel-inner .active.left,
.banner-wrapper .fade-carousel .carousel-inner .active.right {
	opacity: 0;
}

.banner-wrapper .fade-carousel .carousel-inner .active,
.banner-wrapper .fade-carousel .carousel-inner .next.left,
.banner-wrapper .fade-carousel .carousel-inner .prev.right {
	opacity: 1;
}

.banner-wrapper .fade-carousel .carousel-inner .next,
.banner-wrapper .fade-carousel .carousel-inner .prev,
.banner-wrapper .fade-carousel .carousel-inner .active.left,
.banner-wrapper .fade-carousel .carousel-inner .active.right {
	left: 0;
	transform: translate3d(0, 0, 0);
}

.banner-wrapper .fade-carousel .carousel-control {
	z-index: 2;
}

.banner-wrapper .carousel,
.carousel-inner,
.banner-wrapper .carousel-inner .item {
	height: 100%;
	/* margin-top: 100px; */
}

.banner-wrapper .fade-carousel .slides .slide-1:before,
.banner-wrapper .fade-carousel .slides .slide-2:before,
.banner-wrapper .fade-carousel .slides .slide-3:before {
	background: #0B111E;
	bottom: 0;
	content: "";
	left: 0;
	opacity: 0;
	position: absolute;
	right: 0;
	top: 0;
}

.banner-wrapper .fade-carousel .carousel-inner .item img {
	width: 100%;
	height: auto;
}

/*
================================================
9. Header Bottom Arrow
================================================
*/
.bottom_row {
	bottom: 0;
	cursor: pointer;
	position: absolute;
	z-index: 9;
	width: 100%;
	height: 38px;
	text-align: center;
}

.fa.fa-angle-down {
	color: #fff;
	font-size: 32px;
	font-weight: 300;
	padding: 3px 0 0 0;
	opacity: 1;
}

/*
================================================
10. Domain Search
================================================
*/
.domain-search {
	padding: 40px 0;
}

section.domain-search h3 {
	color: #fff;
	font-size: 40px;
	margin: 5px 0 0 0;
	line-height: 38px;
	font-weight: 600;
}

section.domain-search h3 span {
	display: block;
	font-size: 16px;
	font-weight: 300;
}

.domain-search .search {
	background: #fff;
	padding: 2%;
	width: 96%;
	border-radius: 6px;
}

.domain-search .search label {
	background: #25272b;
	padding: 16px 0;
	border-radius: 6px;
	margin: 0;
	color: #fff;
	font-weight: 300;
	font-size: 16px;
	margin: 0;
	display: inline-block;
	width: 18%;
	text-align: center;
}

.domain-search .search input {
	background: none;
	border: none;
	padding: 16px 0 16px 2%;
	display: inline-block;
	width: 60%;
}

.domain-search .search .btn {
	background: #25272b;
	padding: 16px 0;
	border-radius: 6px;
	margin: 0;
	color: #fff;
	font-weight: 300;
	font-size: 16px;
	margin: 0;
	display: inline-block;
	width: 20%;
	text-align: center;
}

.domain-search .search .btn i {
	font-size: 14px;
	padding: 0 0 0 5px;
}

/*
================================================
11. Domain Prices
================================================
*/
section.domain-prices {
	padding-top: 72px;
	padding-bottom: 46px;
}

.domain-prices .prices {
	text-align: center;
}

.domain-prices .prices h4 {
	background: #25272b;
	padding: 12px 0;
	color: #fff;
	text-transform: uppercase;
	font-size: 26px;
}

.domain-prices .prices p {
	font-size: 24px;
	line-height: 32px;
}

.domain-prices .prices p span {
	display: block;
	text-decoration: line-through;
	font-size: 18px;
	font-weight: 300;
}

/*
================================================
12. About Us
================================================
*/
.about-us {
	background: #ebebeb;
	padding-bottom: 75px;
}

.about-us img {
	width: 100%;
	height: auto;
	padding: 0 30px 0 0;
}

.about-us h3 {
	font-size: 30px;
	font-weight: 400;
	color: #25272b;
	margin: 45px 0 26px 0;
}

.about-us p {
	font-size: 16px;
	font-weight: 300;
	line-height: 30px;
	margin: 0 0 30px 0;
}

.about-us .read-more {
	padding: 15px 30px;
	font-size: 16px;
	color: #fff;
	border-radius: 6px;
	display: inline-block;
}

.about-us .read-more:hover {
	background: #25272b;
}

/*
================================================
13. Web Search
================================================
*/
.web-search {
	background: #fff;
	text-align: justify;
	padding-bottom: 73px;
}

.web-search img {
	/* padding: 0 0 0 30px; */
	padding: 0px 40px 40px 40px !important
}

/*
================================================
14. Dream Host
================================================
*/
.dream-host {
	padding: 35px 0 30px;
}

.dream-host h3 {
	color: #fff;
	font-size: 40px;
	margin: 0;
	line-height: 38px;
	font-weight: 600;
}

.dream-host h3 span {
	display: block;
	font-size: 16px;
	font-weight: 300;
}

.dream-host .purchase {
	color: #ffffff;
	border: solid 2px #ffffff;
	border-radius: 6px;
	padding: 10px 30px;
	font-weight: 600;
	font-size: 16px;
	margin: 12px 0 0 0;
}

.dream-host .purchase:hover {
	border: solid 2px #25272b;
}

/*
================================================
15. Our Services
================================================
*/
.our-services .services {
	text-align: center;
}

.our-services .services h3 {
	font-size: 26px;
	font-weight: 400;
}

.our-services .services p {
	font-size: 16px;
	font-weight: 300;
	line-height: 26px;
	padding: 0 12px;
	margin: 0;
}

.our-services .services a {
	border: solid 1px #b7b7b7;
	padding: 8px 20px;
}

.our-services .services a {
	color: #000000;
	font-weight: 600;
}

.our-services .services a:hover {
	border: solid 1px #fd4326;
	background: #fd4326;
	color: #fff;
}

.our-services .services .live-chat-icon1 {
	background: url(../images/enterprise-application-development.png) no-repeat left top;
	display: inline-block;
	margin: 0 0 10px 0;
	width: 76px;
	height: 66px;
}

.our-services .services .domain-transfer-icon1 {
	background: url(../images/hrms-and-Payroll.png) no-repeat left top;
	display: inline-block;
	margin: 0 0 10px 0;
	width: 80px;
	height: 66px;
}

.our-services .services .live-chat-icon2 {
	background: url(../images/application-security-design-consulting.png) no-repeat left top;
	display: inline-block;
	margin: 0 0 10px 0;
	width: 76px;
	height: 66px;
}

.our-services .services .secured-server-icon1 {
	background: url(../images/design-consulting.png) no-repeat left top;
	display: inline-block;
	margin: 6px 0 10px 0;
	width: 60px;
	height: 59px;
}

.our-services .services .secured-server-icon2 {
	background: url(../images/corporate-training.png) no-repeat left top;
	display: inline-block;
	margin: 6px 0 10px 0;
	width: 60px;
	height: 59px;
}

.our-services .services .domain-panel-icon1 {
	background: url(../images/domain-panel-icon.png) no-repeat left top;
	display: inline-block;
	margin: 0 0 10px 0;
	width: 60px;
	height: 63px;
}

/*
================================================
16. What we do
================================================
*/
.what-we-do {
	background: url(../images/what-we-do-bg.jpg) repeat left top;
	padding-top: 73px;
}

.what-we-do .title h2 {
	color: #fff;
}

.what-we-do .title p {
	color: #fff;
}

.what-we-do .what-we {
	text-align: center;
}

.what-we-do .what-we .icon {
	margin: 0 0 25px 0;
	width: 112px;
	height: 112px;
	display: inline-block;
}

.what-we-do .what-we h3 {
	font-size: 26px;
	font-weight: 400;
	color: #fff;
}

.what-we-do .what-we p {
	font-size: 16px;
	font-weight: 300;
	line-height: 26px;
	padding: 0 12px;
	color: #fff;
	margin: 0;
}

/*
================================================
17. Hosting Platforms
================================================
*/
.hosting-platforms {
	background: #ebebeb;
	padding-bottom: 75px;
}

.hosting-platforms .title {
	margin-bottom: 70px;
}

.hosting-platforms .plans {
	background: #ffffff;
	padding: 0 20px 20px 20px;
	text-align: center;
}

.hosting-platforms .plans:hover li,
.hosting-platforms .plans.active li {
	color: #fff;
	border-bottom: solid 1px #fff;
}

.hosting-platforms .plans h3 {
	background: #25272b;
	border: 5px solid #ffffff;
	border-radius: 50%;
	color: #ffffff;
	display: inline-block;
	font-size: 20px;
	height: 90px;
	margin: -50px 0 0;
	padding: 20px 0 0;
	width: 90px;
	font-weight: 400;
}

.hosting-platforms .plans h3 span {
	font-size: 14px;
	display: block;
}

.hosting-platforms .plans li {
	list-style: none;
	font-size: 16px;
	color: #25272b;
	border-bottom: solid 1px #929395;
	padding: 15px 0;
}

.hosting-platforms .plans li:last-child {
	border: none;
}

.hosting-platforms .plans a {
	background: #25272b;
	border-radius: 6px;
	font-size: 16px;
	padding: 15px 30px;
	color: #fff;
	display: inline-block;
}

/*
================================================
18. Testimonials
================================================
*/
.testimonials img {
	width: 100%;
	height: auto;
}

.testimonials .item .tes-cnt {
	background: #ebebeb;
	padding: 25px;
	margin: 0 0 30px 0;
}

.testimonials .item p {
	font-size: 16px;
	margin: 0;
	padding: 0;
}

.testimonials .item p::before {
	content: "";
	display: block;
	font-family: "Fontawesome";
	font-size: 20px;
	margin-right: 15px;
}

.testimonials .item img {
	width: 104px;
	height: 104px;
	border-radius: 50%;
	border: solid 1px #f1f1f1;
	float: left;
	margin: 0 30px 0 0;
}

.testimonials .item .tes-details {
	float: left;
	margin: 30px 0 0 0;
}

.testimonials .item .tes-details h3 {
	font-size: 18px;
	font-weight: 300;
}

.testimonials .item .tes-details h3 span {
	color: #3f4b5d;
}

.testimonials .item .tes-details h4 {
	color: #25272b;
	font-size: 16px;
	font-weight: 300;
}

.testimonials .carousel-indicators {
	left: auto;
	right: 0;
	width: auto;
	z-index: 1;
}

.testimonials .carousel-indicators li {
	width: 14px;
	height: 14px;
	background: #ebebeb;
	padding: 0;
}

/*
================================================
19. Inner Page Styles
================================================
*/
inner-wrapper {
	background: #fff;
}

.top-banner {
	background-image: url(../images/inner-banner.jpg);
	background-position: center center;
	background-size: cover;
	padding: 70px 0 100px;
	text-align: center;
	/* margin-top: 100px; */
}

.top-banner h1 {
	font-family: "Lato", sans-serif;
	font-size: 60px;
	font-weight: 400;
	margin: 0 0 15px;
	text-transform: capitalize;
	color: #fff;
	line-height: 48px;
}

.top-banner p {
	color: #fff;
	margin: 0;
}

.inner-wrapper ul {
	margin: 0;
}

.inner-wrapper li {
	list-style: outside none none;
	padding: 0 0 15px;
}

.inner-wrapper li i {
	padding: 0 10px 0 0;
}

.inner-wrapper .read-more {
	border-radius: 6px;
	color: #ffffff;
	margin: 20px 0 0 0;
	padding: 8px 22px;
	text-transform: uppercase;
	display: inline-block;
}

.inner-wrapper .read-more:hover {
	background: #25272b;
}

/*
================================================
20. Hosting
================================================
*/
.hosting-banner {
	background-image: url(../images/hosting-banner.jpg);
	background-position: center center;
	background-size: cover;
	padding: 60px 0 50px;
}

.hosting-banner h1 {
	font-family: "Lato", sans-serif;
	font-size: 68px;
	font-weight: 900;
	margin: 0 0 10px 0;
	padding: 0;
	text-transform: uppercase;
}

.hosting-banner h2 {
	font-family: "Lato", sans-serif;
	font-size: 40px;
	font-weight: 400;
	margin: 0 0 40px;
	text-transform: capitalize;
	color: #fff;
	line-height: 48px;
}

.hosting-banner h2 span {
	display: block;
	font-weight: 300;
}

.hosting-banner ul {
	margin: 0 0 30px 0;
}

.hosting-banner ul li {
	list-style: none;
	display: inline-block;
	width: 40%;
	color: #fff;
	padding: 0 0 10px 0;
	font-size: 16px;
	font-weight: 300;
}

.hosting-banner ul li i {
	color: #fff;
	font-size: 6px;
	padding: 10px 6px 0 0;
	vertical-align: top;
}

.hosting-banner .price {
	font-size: 30px;
	text-transform: uppercase;
	font-weight: 700;
	line-height: 46px;
	padding: 0;
}

.hosting-banner .price span {
	display: block;
	font-size: 50px;
}

.hosting-banner .price span i {
	font-size: 20px;
	font-style: normal;
	text-transform: lowercase;
	vertical-align: middle;
}

.hosting-banner .buynow {
	margin: 50px 0 0;
	vertical-align: top;
}

.hosting-banner .buynow a {
	text-transform: uppercase;
	color: #fff;
	border-radius: 6px;
	padding: 15px 35px;
	font-weight: 600;
	font-size: 18px;
}

.hosting-banner .sale {
	border: solid 1px #ffffff;
	padding: 30px;
	text-align: center;
	text-transform: uppercase;
	margin: 14px 0 0 15px;
}

.hosting-banner .sale h3 {
	font-weight: 600;
	color: #fff;
	font-size: 75px;
	margin: 0;
}

.hosting-banner .sale h3 span {
	font-weight: 300;
	font-size: 55px;
	display: block;
}

.hosting-banner .sale h2 {
	font-weight: 900;
	font-size: 110px;
	line-height: 100px;
	margin: 0;
}

.hosting-banner .sale h2 span {
	display: block;
	font-weight: 900;
}

/*
================================================
21. Hosting Features
================================================
*/
.hosting-features {
	background: #ebebeb;
	padding-bottom: 40px;
}

.hosting-features .features {
	background: #fff;
	padding: 20px;
	display: inline-block;
	margin: 0 0 30px 0;
}

.hosting-features .features:hover p,
.hosting-features .features:hover h3 {
	color: #fff;
}

.hosting-features .features p {
	margin: 0;
}

.hosting-features .features .icon {
	border-radius: 50%;
	width: 120px;
	height: 120px;
	background: #25272b;
	text-align: center;
	line-height: 120px;
}

/*
================================================
22. Faq
================================================
*/
.faq .panel-group {
	margin-bottom: 0;
}

.faq .panel-heading {
	border-radius: 0;
	padding: 0;
	border: none;
	background-color: inherit;
}

.faq .panel {
	border: none;
	border-radius: 0;
	box-shadow: none;
}

.faq .panel-title {
	position: relative;
}

.faq .panel-title a {
	color: #fff;
	padding: 16px 18px;
	display: block;
	font-size: 18px;
	font-weight: 400;
}

.faq .panel-title a.collapsed {
	color: #25272b;
	background-color: #e1e1e1;
	border: solid 1px #e1e1e1;
}

.faq .panel-title i {
	padding: 0 10px 0 0;
}

.faq .panel-title span {
	font-family: tahoma;
	font-size: 20px;
	padding: 0 5px 0 0;
}

.faq .panel-title a.collapsed:after {
	content: "\f067";
	font-family: "FontAwesome";
	position: absolute;
	right: 15px;
	font-size: 16px;
}

.faq .panel-title a:after {
	content: "\f068";
	font-family: "FontAwesome";
	position: absolute;
	right: 15px;
	font-size: 16px;
}

.faq .panel-body {
	background: #e1e1e1;
}

.faq .panel-group .panel+.panel {
	margin-top: 15px;
}

/*
================================================
23. Call Back
================================================
*/
.call-back {
	/* background-image: url('../images/banner/ribbon.png'); */
	background: #ffffff;
	background-position: center;
	background-size: cover;
	background-repeat: no-repeat;
}

.call-back .title p {
	margin-bottom: 0;
}

.call-back form label {
	font-size: 13px;
	font-weight: 700;
}

.asterik {
	color: red !important;
	font-weight: 400;
}

.btn-contact-submit:hover {
	border: 1px solid green;
	background: transparent;
	color: #000 !important;
	font-weight: 600;
}


.call-back form textarea {
	background: #fff;
	border: solid 1px #ccc;
	border-radius: 6px;
	padding-left: 1%;
	padding-top: 5px;
	width: 100%;
	margin: 0 0 14px 0;
	color: #333;
	font-size: 14px;
	height: 100px;
}

/* 
.call-back form .btn {
	border: 0 none;
	border-radius: 5px;
	color: #ffffff;
	font-size: 16px;
	background-color: green;
	font-weight: 400;
	margin: 15px 0 0;
	padding: 10px 30px;
	text-align: center;
	text-transform: uppercase;
	transition: all 0.3s ease 0s;
	float: right;
} */

/*
================================================
24. Domain Register
================================================
*/
.domain-register-wrapper {
	padding-bottom: 40px;
}

.domain-register {
	box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
	padding: 24px 10px 30px;
	margin: 0 0 30px 0;
	text-align: center;
}

.domain-register h3 {
	font-size: 40px;
	margin: 0;
}

.domain-register p {
	font-size: 13px;
	line-height: 18px;
}

.domain-register h4 {
	font-size: 24px;
	line-height: 32px;
}

.domain-register h4 span {
	text-decoration: line-through;
	font-size: 16px;
	padding: 0 0 0 5px;
}

.domain-register a {
	background: #25272b;
	border-radius: 6px;
	color: #ffffff;
	display: inline-block;
	font-size: 16px;
	padding: 15px 30px;
}

/*
================================================
25. Domain Transfer
================================================
*/
.inner-wrapper.what-we-do {
	background: #fff;
}

.inner-wrapper.what-we-do h3 {
	color: #2a2a2a;
}

.inner-wrapper.what-we-do p {
	color: #2a2a2a;
}

.inner-wrapper.what-we-do .what-we i {
	width: 112px;
	height: 112px;
	line-height: 112px;
	color: #fff;
	font-size: 40px;
	border-radius: 0 50% 0 50%;
	margin: 0 0 20px 0;
}

/*
================================================
26. Domain Price
================================================
*/
.tables .table-custom {
	width: 100%;
}

.tables .table-custom tbody tr:first-child {
	-moz-border-bottom-colors: none;
	-moz-border-left-colors: none;
	-moz-border-right-colors: none;
	-moz-border-top-colors: none;
	border-color: -moz-use-text-color -moz-use-text-color #2d2e2e;
	border-image: none;
	border-style: none none solid;
	border-width: medium medium 1px;
}

.tables .table-custom th,
.table-custom td {
	padding: 13px 23px;
}

.tables .table-custom tbody tr {
	-moz-border-bottom-colors: none;
	-moz-border-left-colors: none;
	-moz-border-right-colors: none;
	-moz-border-top-colors: none;
	border-color: #e5e5e5 -moz-use-text-color;
	border-image: none;
	border-style: solid none;
	border-width: 1px medium;
}

.tables .table-custom tbody tr:hover {
	background: #f5f5f5;
}

.tables .striped tr:nth-child(even) {
	background-color: #f5f5f5
}

.tables table.striped tr:hover {
	background: none;
}

.tables table.striped tr:hover:nth-child(even) {
	background-color: #f5f5f5
}

.tables .table-custom.table-dark tr th {
	color: #fff;
}

.tables .table-custom.border,
.border td,
.border th {
	border: 1px solid #eee;
}

.tables .table-custom.border tr:hover {
	background: none;
}

/*
================================================
27. About Us Page
================================================
*/
.inner-wrapper.about {
	padding-bottom: 40px;
}

.inner-wrapper .callout {
	background: #f5f5f5;
	padding: 20px;
	margin: 0 0 30px 0;
}

.inner-wrapper .about-list li {
	display: inline-block;
	width: 40%;
}

/*
================================================
28. Testimonials Page
 ================================================
*/
.inner-wrapper.testimonials .divider {
	border-top: 1px solid #efefef;
	margin: 50px 0 0;
}

.inner-wrapper.testimonials .carousel-indicators {
	left: auto;
	right: 0;
	width: auto;
	z-index: 1;
}

.tes-wrapper {
	background-color: #FFF;
	border: 1px solid #e5e5e5;
	text-align: center;
	border-radius: 6px;
	padding: 0;
	margin: 50px 0 0 0;
}

.tes-wrapper img {
	display: block;
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.tes-wrapper span {
	border-radius: 100px;
	display: inline-block;
	height: 100px;
	width: 100px;
	margin-bottom: 10px;
	margin-top: -50px;
	-moz-transition: all 0.3s;
	-webkit-transition: all 0.3s;
	-ms-transition: all 0.3s;
	-o-transition: all 0.3s;
}

.tes-wrapper p {
	padding: 0 10px;
}

.tes-wrapper a {
	display: inline-block;
	margin: 20px 0;
}

.tes-wrapper:hover span {
	-moz-transform: scale(1.1);
	-ms-transform: scale(1.1);
	-o-transform: scale(1.1);
	-webkit-transform: scale(1.1);
	transform: scale(1.2);
}

.tes-wrapper h3 {
	border-top: 1px solid #e5e5e5;
	margin: 20px 0 0;
	padding: 20px 0 30px;
	font-weight: 400;
}

/*
================================================
29. Our Team
================================================
*/
.our-team .team {
	border: solid 1px #f0f0f0;
	float: left;
	width: 100%;
}

.our-team .team img {
	width: 100%;
	height: auto;
}

.our-team .team h3 {
	font-size: 20px;
	line-height: 26px;
	color: #000;
	margin: 0 0 20px 0;
	font-weight: 400;
}

.our-team .team:hover h3 {
	color: #fff;
}

.our-team .team h3 span {
	font-size: 16px;
	display: block;
}

.our-team .team .team-cnt {
	background: #ffffff;
	padding: 22px;
	float: left;
	width: 100%;
}

.our-team .team .team-cnt ul {
	margin: 0;
}

.our-team .team .team-cnt li {
	list-style: none;
	padding: 0;
}

.our-team .team .team-cnt .address li i {
	display: inline-block;
	margin: 5px 5px 0 0;
	vertical-align: top;
	color: #2a2a2a;
}

.our-team .team:hover .team-cnt .address li i {
	color: #fff;
}

.our-team .team .team-cnt ul li p {
	display: inline-block;
	margin-bottom: 5px;
	font-size: 14px;
}

.our-team .team:hover .team-cnt ul li p {
	color: #fff;
}

.our-team .team:hover .team-cnt ul li p a {
	color: #fff;
}

.our-team .team .team-cnt .social-icons {
	margin-top: 20px;
}

.our-team .team .team-cnt .social-icons li {
	display: inline-block;
}

.our-team .team .team-cnt .social-icons i {
	width: 30px;
	height: 30px;
	line-height: 28px;
	font-size: 16px;
	border-radius: 50%;
	text-align: center;
	border: solid 1px #ccc;
	color: #ccc;
	margin: 0 10px 0 0;
	padding: 0;
}

.our-team .team:hover .team-cnt .social-icons i {
	border: solid 1px #fff;
	color: #fff;
}

/*
================================================
30. Login / Sign Up
================================================
*/
.login {
	background: #fff;
}

.login h3 {
	margin: 0 0 24px 0;
}

/* Checkbox styling */
.login form [type="checkbox"]:not(:checked),
.login form [type="checkbox"]:checked {
	position: absolute;
	left: -9999px;
}

.login form [type="checkbox"]:not(:checked)+label,
.login form [type="checkbox"]:checked+label {
	position: relative;
	padding-left: 30px;
	cursor: pointer;
	font-weight: 400;
	font-size: 14px;
	color: #000;
}

/* checkbox aspect */
.login form [type="checkbox"]:not(:checked)+label:before,
.login form [type="checkbox"]:checked+label:before {
	content: '';
	position: absolute;
	left: 0;
	top: 2px;
	width: 20px;
	height: 20px;
	border: solid 1px #202020;
	border-radius: 50%;
}

/* checked mark aspect */
.login form [type="checkbox"]:not(:checked)+label:after,
.login form [type="checkbox"]:checked+label:after {
	content: '✔';
	position: absolute;
	top: 8px;
	left: 5px;
	font-size: 12px;
	line-height: 0.8;
	color: #000;
	transition: all .2s;
}

/* checked mark aspect changes */
.login form [type="checkbox"]:not(:checked)+label:after {
	opacity: 0;
	transform: scale(0);
}

.login form [type="checkbox"]:checked+label:after {
	opacity: 1;
	transform: scale(1);
}

.login form .forget-password a {
	color: #000000;
	font-size: 14px;
	font-weight: 400;
}

.login form .btn {
	width: 100%;
}

.login .signup {
	border-left: 1px solid #cccccc;
	float: left;
	margin: 0 0 0 15px;
	padding: 0 0 0 45px;
}

/*
================================================
31. 404
================================================
*/
.error-page {
	text-align: center;
}

.error-page i {
	color: #ccc;
	font-size: 160px;
}

.error-page h3 {
	font-size: 60px;
}

.error-page h4 {
	font-size: 30px;
	color: #2a2a2a;
	font-weight: 300;
}

.error-page a {
	border-radius: 6px;
	color: #ffffff;
	margin: 20px 0 0 0;
	padding: 10px 22px;
	text-transform: uppercase;
	display: inline-block;
}

.error-page a:hover,
.error-page a:focus {
	background: #25272b;
}

/*
================================================
32. Blog Page
================================================
*/
.inner-wrapper.blog .thumbnail {
	background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
	border: medium none;
	border-radius: 0;
	margin-bottom: 50px;
	padding: 0;
	position: relative;
}

.inner-wrapper.blog .thumbnail:hover .caption {
	box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
}

.inner-wrapper.blog .thumbnail:hover .caption a.readmore {
	color: #fff;
}

.inner-wrapper.blog .thumbnail:hover .caption a {
	color: #6c6c6c;
}

.inner-wrapper.blog .thumbnail:hover .hover-img>img {
	opacity: 0.6;
	transform: scale(1.1);
	transition-duration: 0.5s;
}

.inner-wrapper.blog .thumbnail img {
	width: 100%;
}

.inner-wrapper.blog .hover-img {
	background: #2d2e2e;
	display: block;
	overflow: hidden;
	position: relative;
	text-align: center;
}

.hover-img>img {
	min-height: 100%;
	transition-duration: 0.5s;
}

.inner-wrapper.blog .date {
	position: absolute;
	top: 0;
	left: 0;
}

.inner-wrapper.blog ul {
	margin: 0;
	padding: 0;
}

.inner-wrapper.blog ul li {
	display: inline-block;
	list-style: none;
}

.inner-wrapper.blog .date li {
	padding: 10px 15px;
	border-left: solid 1px #f5f5f5;
	font-weight: 600;
	font-size: 16px;
	color: #fff;
}

.inner-wrapper.blog .date li:first-child {
	border: none;
}

.inner-wrapper.blog .caption {
	background: #f8f8f8;
	margin: 0;
	padding: 20px 20px 30px;
}

.inner-wrapper.blog .caption h3 {
	margin: 0 0 10px 0;
}

.blog .caption h3 a {
	font-size: 22px;
}

.inner-wrapper.blog .caption h3 a:focus {
	text-decoration: none;
}

.inner-wrapper.blog .caption a {
	color: #000000;
	font-weight: 600;
}

.inner-wrapper.blog .caption .a.readmore {
	border: solid 1px #b7b7b7;
	padding: 8px 20px;
}

.inner-wrapper.blog .caption p {
	line-height: 26px;
	margin: 0 0 20px 0;
}

.inner-wrapper.blog .details {
	margin: 0 0 20px 0;
}

.inner-wrapper.blog .details li {
	margin: 0 10px 0 0;
	font-weight: 600;
	color: #6c6c6c;
}

.inner-wrapper.blog .details li span {
	color: #000;
	font-weight: 600;
}

.inner-wrapper.blog .list-inline li a {
	background: #000;
	color: #fff;
	padding: 5px;
}

.blog-pagination {
	text-align: center;
}

.blog-pagination li {
	display: inline-block;
	margin: 0 5px 0 0;
	padding: 0;
}

.blog-pagination li a {
	padding: 10px 16px;
}

.blog-pagination .pagination>li>a,
.blog-pagination .pagination>li>span {
	color: #000;
}

.blog-pagination .pagination>li>a:focus,
.blog-pagination .pagination>li>a:hover,
.blog-pagination .pagination>li>span:focus,
.blog-pagination .pagination>li>span:hover {
	color: #fff;
}

/*
================================================
33. Blog Details
================================================
*/
.inner-wrapper .single-post h3 {
	margin: 0 0 10px 0;
	text-align: left;
	font-size: 24px;
}

.inner-wrapper .single-post .list-inline li a {
	background: #000000 none repeat scroll 0 0;
	color: #fff;
	padding: 8px;
}

.inner-wrapper .single-post .list-inline li a:hover {
	color: #fff;
}

.inner-wrapper .single-post .thumbnail {
	border: none;
	padding: 0;
	margin: 0 0 20px 0;
	position: relative;
}

.inner-wrapper .single-post .thumbnail .date {
	position: absolute;
	top: 0;
	left: 0;
}

.inner-wrapper .single-post .thumbnail .date li {
	padding: 10px 15px;
	border-left: solid 1px #f5f5f5;
	font-weight: 600;
	font-size: 16px;
	display: inline-block;
	color: #fff;
}

.inner-wrapper .single-post .date li:first-child {
	border: none;
}

.inner-wrapper .single-post .details {
	margin: 0 0 20px 0;
}

.inner-wrapper .single-post .details li {
	margin: 0 10px 0 0;
	font-weight: 600;
	color: #6c6c6c;
	display: inline-block;
}

.inner-wrapper .single-post .details li span {
	color: #000;
	font-weight: 600;
}

.inner-wrapper .single-post img {
	width: 100%;
}

.inner-wrapper .single-post .thumbnail .caption {
	padding: 10px 0 10px 0;
}

.inner-wrapper .single-blog {
	margin: 0 0 30px 0;
}

.inner-wrapper .single-post .form-item {
	background: #fff none repeat scroll 0 0;
	border-radius: 0;
	box-shadow: none;
	height: 45px;
	padding-left: 15px;
}

.inner-wrapper .single-post .btn-1 {
	border: medium none;
	border-radius: 0;
	color: #fff;
	padding: 12px 25px;
	text-transform: uppercase;
	transition: all 0.3s ease 0s;
}

.inner-wrapper .single-post blockquote {
	border-left: 3px solid #eee;
	font-size: 14px;
	margin: 0 0 20px;
	padding: 10px 20px;
}

.inner-wrapper .single-post .list-inline {
	margin: 0 0 20px 0;
}

.inner-wrapper .single-post .sidebar h3 {
	margin-bottom: 26px;
	font-size: 22px;
}

.inner-wrapper .single-post .sidebar h3::after {
	content: "";
	display: block;
	height: 3px;
	margin: 10px 0 0;
	width: 40px;
}

.inner-wrapper .single-post .meta {
	font-size: 12px;
}

.align-right {
	text-align: right;
}

.single-post form {
	margin: 0 0 20px;
}

.inner-wrapper .single-post h4 {
	font-size: 14px;
	color: #000000;
	margin: 0 0 5px 0;
}

.inner-wrapper .sidebar #custom-search-input {
	padding: 3px;
	border: solid 1px #E4E4E4;
	border-radius: 6px;
	background-color: #fff;
	margin: 0 15px 24px 15px;
}

.inner-wrapper .sidebar #custom-search-input input {
	border: 0;
	box-shadow: none;
}

.inner-wrapper .sidebar #custom-search-input button {
	margin: 2px 0 0 0;
	background: none;
	box-shadow: none;
	border: 0;
	color: #666666;
	padding: 0 8px 0 10px;
	border-left: solid 1px #ccc;
}

.inner-wrapper .sidebar #custom-search-input button:hover {
	border: 0;
	box-shadow: none;
	border-left: solid 1px #ccc;
}

.inner-wrapper .sidebar #custom-search-input .glyphicon-search {
	font-size: 23px;
}

.inner-wrapper .single-post .latest-blogs {
	margin: 0 0 10px 0;
}

.inner-wrapper .single-post .latest-blog {
	border-bottom: 1px solid #c1c1c1;
	margin-bottom: 15px;
	padding-bottom: 15px;
}

.inner-wrapper .single-post .latest-blog:last-child {
	border: none;
}

.inner-wrapper .single-post .meta-info {
	padding: 0;
	margin: 5px 0 0 0;
}

.inner-wrapper .single-post .meta-info h3 a {
	color: #000000;
}

.inner-wrapper .sidebar .meta-info h3 a:hover {
	color: #333;
}

.inner-wrapper .single-post .comments {
	background: #f5f5f5;
	margin: 0 0 30px;
	padding: 20px;
	float: left;
	width: 100%;
}

.inner-wrapper .single-post .comments p {
	margin: 10px 0 0 0;
}

.inner-wrapper .single-post .post {
	margin-bottom: 0;
}

.inner-wrapper .sidebar .unordered-list ul li::before {
	color: #000;
	content: "";
	font-family: "FontAwesome";
	font-size: 14px;
	padding: 0 10px 0 0;
}

.inner-wrapper .sidebar .unordered-list ul li+li {
	margin: 10px 0 0;
}

.inner-wrapper .sidebar .unordered-list ul {
	margin: 0 0 30px 0;
}

.inner-wrapper .sidebar .unordered-list ul li a:hover,
.inner-wrapper .sidebar .unordered-list ul li a:focus {
	color: #333;
	text-decoration: none;
}

.inner-wrapper .single-post .unordered-list ul li {
	list-style: none;
}

.inner-wrapper .single-post .unordered-list ul li::before {
	content: "\f105";
	font-family: "FontAwesome";
	font-size: 14px;
	padding: 0 10px 0 0;
}

.inner-wrapper .single-post .unordered-list.no-space {
	margin: 0 0 10px;
}

.inner-wrapper .single-post .unordered-list ul {
	margin: 0 0 10px 30px;
}

.inner-wrapper .single-post .ordered-list ol {
	margin: 0 0 10px 46px;
}

.inner-wrapper .single-post .ordered-list {
	margin: 0 0 10px;
}

.inner-wrapper .sidebar .unordered-list ul {
	margin: 0 0 30px 0;
}

.inner-wrapper .sidebar .unordered-list ul li a {
	-webkit-transform: translateZ(0);
	-moz-transform: translateZ(0);
	-ms-transform: translateZ(0);
	-o-transform: translateZ(0);
	transform: translateZ(0);
	transition-duration: 0.3s;
	transition-property: transform;
	display: inline-block;
}

.inner-wrapper .sidebar .unordered-list ul li a:hover,
.inner-wrapper .sidebar .unordered-list ul li a:focus {
	-webkit-transform: translateX(10px);
	-moz-transform: translateX(10px);
	-ms-transform: translateX(10px);
	-o-transform: translateX(10px);
	transform: translateX(10px);
}

.inner-wrapper .sidebar .tags li {
	display: inline-block;
	list-style: none;
	background: #f5f5f5;
	padding: 5px 12px;
	margin: 0 5px 5px 0;
}

.inner-wrapper .sidebar .tags li:hover a {
	color: #fff;
}

/*
================================================
34. Contact Us Page
================================================
*/
.get-in-touch {
	background-image: url(../images/get-in-touch-bg.jpg);
	background-position: center center;
	background-size: cover;
}

.get-in-touch .title h2 {
	color: #fff;
}

.get-in-touch .title p {
	color: #fff;
}

.get-in-touch .title h2::before {
	background: rgba(0, 0, 0, 0) url(../images/h2-before-white.png) no-repeat scroll left top;
}

.get-in-touch .title h2::after {
	background: rgba(0, 0, 0, 0) url(../images/h2-after-white.png) no-repeat scroll left top;
}

.get-in-touch .details {
	text-align: center;
}

.get-in-touch .details h3 {
	color: #fff;
	font-size: 26px;
	font-weight: 400;
	margin: 0 0 15px 0;
}

.get-in-touch .details i {
	margin: 0 0 15px 0;
	font-size: 50px;
	color: #fff;
}

.get-in-touch .details p {
	color: #acacac;
	font-size: 15px;
	line-height: 26px;
	margin: 0;
	padding: 0 20px;
}

.get-in-touch .details a {
	color: #acacac;
}

/*
================================================
35. Contact Details
================================================
*/
.contact-details {
	padding: 20px 0 17px;
}

.contact-details i {
	border: 1px solid #ffffff;
	border-radius: 50%;
	color: #ffffff;
	display: inline-block;
	font-size: 26px;
	height: 60px;
	line-height: 60px;
	text-align: center;
	width: 60px;
	margin: 0 15px 0 0;
}

.contact-details h3 {
	color: #ffffff;
	display: inline-block;
	font-size: 24px;
	font-weight: 300;
	margin: 0;
}

/*
================================================
36. Footer Wrapper
================================================
*/
.footer-wrapper {
	background: #25272b;
	padding-top: 30px;
	padding-bottom: 30px;
}

.footer-wrapper h3 {
	color: #fff;
	font-weight: 300;
	font-size: 20px;
	margin: 0 0 25px 0;
}

.footer-wrapper p,
.footer-wrapper a {
	color: #acacac;
}

.footer-wrapper p strong {
	color: #fff;
	font-weight: 400;
}

.footer-wrapper ul {
	margin: 0;
}

.footer-wrapper li {
	list-style: none;
	padding: 0 0 15px 0;
	font-size: 14px !important;
}

.footer-wrapper li i {
	padding: 0 10px 0 0;
}

.footer-wrapper .border {
	border-left: 1px solid #737374;
	min-height: 242px;
}

/*
================================================
37. Footer
================================================
*/
footer {
	padding: 20px 0;
	float: left;
	width: 100%;
	background: #141618;
}

footer p {
	margin: 5px 0 0 0;
	color: #6c6c6c;
	font-size: 13px;
	text-align: center;
	border-radius: 6px;
	padding: 10px 20px;
}

footer .social-icons {
	margin: 13px 0 0 0;
}

footer .social-icons li {
	list-style: none;
	display: inline-block;
	padding: 0 0 0 10px;
}

footer .social-icons li i {
	color: #6c6c6c;
	border: solid 1px #6c6c6c;
	width: 28px;
	height: 28px;
	line-height: 28px;
	text-align: center;
}

footer .social-icons li:hover i {
	opacity: 0.7;
}

/* Scroll Up */
#scrollUp {
	background: #000;
	border-radius: 50%;
	bottom: 20px;
	color: #fff;
	display: block;
	font-size: 25px;
	height: 40px;
	line-height: 0;
	position: fixed;
	right: 20px;
	text-align: center;
	text-decoration: none !important;
	transition: all 0.5s cubic-bezier(0, 0, 0, 1) 0s;
	width: 40px;
	z-index: 1000;
}

#scrollUp:hover {
	background: #000;
	color: #fff;
}

#scrollUp i {
	display: block;
	padding-top: 5px;
}

/*
================================================
38. Media Quries 
================================================
*/
@media (min-width: 992px) and (max-width: 1024px) {

	/* Nav */
	.navbar-nav>li>a {
		padding: 0 12px;
	}

	.navbar {
		margin: 22px 0 0;
	}

	.navbar-nav>li>.dropdown-menu {
		margin-top: 34px;
	}

	/* Cart and Sign Up */
	.cart-signup {
		margin-left: 12px;
	}

	.cart-signup li .sign-up {
		margin-left: 12px;
	}

	/* Banner */
	.banner-wrapper .hero {
		top: 22%;
	}

	.banner-wrapper .hero h1 {
		font-size: 80px;
	}

	.banner-wrapper .hero h2 {
		font-size: 46px;
		line-height: 52px;
		margin: 0 0 20px;
	}

	.banner-wrapper .btn-hero {
		padding: 6px 14px;
	}

	/* Domain Search */
	section.domain-search h3 {
		font-size: 34px;
	}

	section.domain-search h3 span {
		font-size: 14px;
	}

	.domain-search .search .btn {
		font-size: 14px;
	}

	/* About Us */
	.about-us h3 {
		margin-top: 8px;
	}

	/* Dream Host */
	.dream-host h3 {
		font-size: 34px;
	}

	.dream-host h3 span {
		font-size: 16px;
		line-height: 22px;
	}

	/* Hosting */
	.hosting-banner h1 {
		font-size: 66px;
	}

	.hosting-banner h2 {
		font-size: 38px;
	}
}

@media (min-width: 768px) and (max-width: 991px) {

	/* Nav */
	nav.pull-right {
		float: left !important;
	}

	.navbar-nav>li>a {
		padding: 0 12px;
	}

	.navbar-nav>li:first-child>a {
		padding-left: 0;
	}

	.affix .logo-wrapper {
		padding: 20px 0 21px;
	}

	.affix .navbar-nav>li>.dropdown-menu {
		margin: 32px 0 0;
	}

	.navbar {
		margin-top: 20px;
	}

	.navbar-nav>li>.dropdown-menu {
		margin-top: 30px;
	}

	/* Cart and Sign Up */
	.cart-signup {
		margin-left: 12px;
	}

	.cart-signup li .sign-up {
		margin-left: 12px;
	}

	/* Top Wrapper */
	.top-wrapper ul li {
		padding: 0 0 0 14px;
	}

	/* Banner */
	.banner-wrapper .hero {
		top: 21%;
	}

	.banner-wrapper .hero h1 {
		font-size: 55px;
	}

	.banner-wrapper .hero h2 {
		font-size: 30px;
		line-height: 38px;
		margin: 0 0 20px;
	}

	.banner-wrapper .btn-hero {
		padding: 6px 14px;
	}

	/* Domain Search */
	section.domain-search h3 {
		margin: 0 0 25px 0;
	}

	/* About Us */
	.about-us img {
		padding: 0;
	}

	.about-us h3 {
		margin-top: 0;
		font-size: 22px;
	}

	.about-us p {
		font-size: 14px;
		line-height: 26px;
	}

	/* Dream Host */
	.dream-host h3 span {
		font-size: 15px;
	}

	/* Our Services */
	.our-services {
		padding-bottom: 10px;
	}

	.our-services .services {
		margin-bottom: 60px;
	}

	/* What we do */
	.what-we-do {
		padding-bottom: 10px;
	}

	.what-we-do .what-we {
		margin-bottom: 60px;
	}

	/* Hosting Platforms */
	.hosting-platforms {
		padding-bottom: 0;
	}

	.hosting-platforms .plans {
		margin-bottom: 70px;
	}

	/* Hosting */
	.hosting-banner .sale {
		margin-left: 0;
		margin-bottom: 40px;
	}

	/* Hosting Features */
	.hosting-features .features {
		text-align: center;
	}

	.hosting-features .features .icon {
		display: inline-block;
		margin: 0 0 20px 0;
	}

	/* Testimonials */
	.testimonials .carousel-indicators {
		bottom: -46px;
	}

	/* Our Team */
	.our-team {
		padding-bottom: 40px;
	}

	.our-team .team {
		margin-bottom: 30px;
	}

	/* Contact Details */
	.contact-details h3 {
		font-size: 18px;
	}

	.contact-details i {
		font-size: 22px;
		height: 50px;
		line-height: 50px;
		margin: 0 8px 0 0;
		width: 50px;
	}

	/* Get In Touch */
	.get-in-touch {
		padding-bottom: 30px;
	}

	.get-in-touch .details {
		margin-bottom: 40px;
	}
}

@media (max-width: 767px) {

	/* Common Css */
	section h2 {
		font-size: 30px;
		line-height: 32px;
	}

	section .title p {
		font-size: 20px;
		margin: 0;
	}

	.top-banner h1 {
		font-size: 44px;
	}

	.domain-prices .prices p {
		margin: 0 0 40px;
	}

	section.domain-prices {
		padding-bottom: 30px;
	}

	/* Nav */
	.navbar-default .navbar-collapse {
		float: none;
		border: none;
	}

	nav.navbar.navbar-default.pull-right {
		float: left !important;
		width: 100%;
	}

	.navbar {
		margin: 0;
	}

	.navbar-brand {
		display: block;
	}

	.navbar-toggle {
		margin-top: 18px;
		margin-right: 0;
	}

	.navbar-default .navbar-collapse,
	.navbar-default .navbar-form {
		border-color: inherit;
	}

	.navbar-nav {
		background: #fff;
		float: left;
		margin: 10px 0 0;
		width: 100%;
		padding: 5px 0 10px 0;
	}

	.navbar-nav>li>a {
		padding: 10px 20px;
	}

	.navbar-default .navbar-nav>li>a {
		color: #000;
	}

	.navbar-default .navbar-nav>li>a>i {
		position: static;
	}

	.navbar-nav>li {
		border-right: none;
	}

	.navbar-nav .open .dropdown-menu .dropdown-header,
	.navbar-nav .open .dropdown-menu>li>a {
		padding: 10px 15px 10px 25px;
	}

	/* Banner */
	.banner-wrapper .hero {
		display: none;
	}

	.banner-wrapper .fade-carousel .slide-arrows .carousel-control span {
		height: 30px;
		line-height: 25px;
		width: 30px;
	}

	/* Domain Search */
	section.domain-search h3 {
		margin: 0 0 25px 0;
		font-size: 22px;
	}

	section.domain-search h3 span {
		line-height: 22px;
	}

	.domain-search .search label {
		width: 100%;
	}

	.domain-search .search input {
		width: 100%;
	}

	.domain-search .search .btn {
		width: 100%;
	}

	/* Domain Prices */
	section.domain-prices .title p {
		font-size: 20px;
		margin: 0;
	}

	/* About Us */
	.about-us img {
		padding: 0;
		margin: 0 0 30px 0;
	}

	.about-us h3 {
		margin-top: 0;
		font-size: 22px;
	}

	.about-us p {
		font-size: 14px;
		line-height: 26px;
	}

	/* Web Search  */
	.web-search img {
		margin: 30px 0 0;
	}

	/* Dream Host */
	.dream-host h3 {
		margin: 0 0 25px 0;
		font-size: 20px;
	}

	.dream-host h3 span {
		line-height: 22px;
	}

	.dream-host .purchase {
		float: left !important;
	}

	/* Our Services */
	.our-services {
		padding-bottom: 10px;
	}

	.our-services .services {
		margin-bottom: 60px;
	}

	/* What we do */
	.what-we-do {
		padding-bottom: 10px;
	}

	.what-we-do .what-we {
		margin-bottom: 60px;
	}

	/* Hosting Platforms */
	.hosting-platforms {
		padding-bottom: 0;
	}

	.hosting-platforms .plans {
		margin-bottom: 70px;
	}

	/* Testimonials */
	.testimonials .carousel-indicators {
		bottom: -46px;
	}

	/* Hosting */
	.hosting-banner h1 {
		font-size: 28px;
	}

	.hosting-banner h2 {
		font-size: 20px;
		line-height: 28px;
	}

	.hosting-banner ul li {
		width: 100%;
	}

	.hosting-banner .sale {
		margin-left: 0;
		margin-bottom: 40px;
	}

	/* Hosting Features */
	.hosting-features .features {
		text-align: center;
	}

	.hosting-features .features .icon {
		display: inline-block;
		margin: 0 0 20px 0;
	}

	/* Contact Details */
	.contact-details {
		padding-bottom: 0;
		padding-top: 40px;
	}

	.contact-details h3 {
		margin-bottom: 60px;
	}

	/* Call Back */
	.call-back .title p {
		font-size: 14px;
		line-height: 20px;
	}

	/* About Us */
	.inner-wrapper .read-more {
		margin-bottom: 30px;
	}

	.inner-wrapper .about-list li {
		width: 100%;
	}

	/* Testimonial Page */
	.inner-wrapper.testimonials {
		padding-bottom: 0;
	}

	.tes-wrapper {
		margin-bottom: 100px;
	}

	/* Get In Touch */
	.get-in-touch {
		padding-bottom: 30px;
	}

	.get-in-touch .details {
		margin-bottom: 40px;
	}

	/* Single Post */
	.inner-wrapper .single-post .meta-info {
		padding: 5px 15px;
	}

	.inner-wrapper .single-post .latest-blog {
		border: none;
	}

	.inner-wrapper .sidebar #custom-search-input {
		margin-top: 15px;
	}

	/* Our Team */
	.our-team {
		padding-bottom: 40px;
	}

	.our-team .team {
		margin-bottom: 30px;
	}

	/* Login / Sign Up */
	.login .signup {
		border-left: medium none;
		border-top: 1px solid #cccccc;
		float: left;
		margin: 40px 0 0;
		padding: 35px 0 0;
	}

	/* Footer Wrapper */
	.footer-wrapper .border {
		border-bottom: 1px solid #737374;
		border-left: none;
		margin: 0 0 30px;
		min-height: auto;
		padding: 16px 0 0;
	}

	/* Footer*/
	footer {
		text-align: center;
	}

	footer p {
		margin: 30px 0;
	}

	footer .social-icons {
		float: none !important;
		margin: 0;
	}

}


#sendmessage {
	color: green;
	border: 1px solid green;
	display: none;
	text-align: center;
	padding: 15px;
	font-weight: 600;
	margin-bottom: 15px;
}

#errormessage {
	color: red;
	display: none;
	border: 1px solid red;
	text-align: center;
	padding: 15px;
	font-weight: 600;
	margin-bottom: 15px;
}

#sendmessage.show,
#errormessage.show,
.show {
	display: block;
}

/*
================================================
39 . HRMS
================================================
*/
.loan-management {
	width: 100%;
}

.asset-management {
	width: 100% !important;
	padding: 20px;
}

/* -------------- 40 . New Header -----------------------*/

nav {
	position: fixed;
	z-index: 99;
	width: 100%;

	background: #ffffff;
}

nav .wrapper {
	position: relative;
	max-width: 1300px;
	padding: 0px 30px;
	height: 70px;
	line-height: 70px;
	margin: auto;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.wrapper .logo a {
	color: #000000;
	font-size: 30px;
	font-weight: 600;
	text-decoration: none;
}

.wrapper .nav-links {
	display: inline-flex;
}

.nav-links li {
	list-style: none;
}

.nav-links li a {
	color: #000000;
	text-decoration: none;
	font-size: 15px;
	font-weight: 500;
	padding: 9px 15px;
	border-radius: 5px;
	position: relative;
	transition: all 0.3s ease;
}

.nav-links li a::after {
	content: '';
	position: absolute;
	left: 0;
	bottom: 0;
	height: 1px;
	border-bottom: 1px solid #898998;
	transition: width 0.3s ease;
	/* Add transition for width */
}

.nav-links li a:hover::after {
	width: 100%;
	/* Expand the underline to 100% width on hover */
}

.nav-links .mobile-item {
	display: none;
}

.nav-links .drop-menu {
	position: absolute;
	background: #ffffff;
	width: 180px;
	line-height: 45px;
	top: 85px;
	opacity: 0;
	visibility: hidden;
	box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.nav-links li:hover .drop-menu,
.nav-links li:hover .mega-box {
	transition: all 0.3s ease;
	top: 70px;
	opacity: 1;
	visibility: visible;
}

.drop-menu li a {
	width: 100%;
	display: block;
	padding: 0 0 0 15px;
	font-weight: 400;
	border-radius: 0px;
}

.mega-box {
	position: absolute;
	left: 0;
	width: 100%;
	padding: 0 30px;
	top: 85px;
	opacity: 0;
	visibility: hidden;
}

.mega-box .content {
	background: #ffffff;
	padding: 20px 15px;
	/* display: flex; */
	width: 100%;
	justify-content: space-between;
	box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.mega-box .content .row {
	width: calc(25% - 30px);
	line-height: 45px;
}

.content .row img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.content .row header {
	color: #000000;
	font-size: 17px;
	font-weight: 500;
}

.content .row .mega-links {
	margin-left: -40px;
	border-left: 1px solid rgba(255, 255, 255, 0.09);
}

.row .mega-links li {
	/* padding: 0 20px; */
}

.row .mega-links li a {
	padding: 0px;
	line-height: 2rem;
	color: #000000;
	font-size: 14px;
	font-weight: 400;
	list-style-type: disc;
	white-space: nowrap;
	display: block;
}

#heading-of-header {
	margin-bottom: 0px !important;
	border-bottom: 1px solid #989898;
	padding-bottom: 7px;
}

#h-6 {
	margin-top: 45px !important;
	font-size: 17px;
	font-weight: 700;
	height: 50px;
}

#h-8 {
	margin-top: 80px !important;
	font-size: 17px;
	font-weight: 700;
	height: 50px;
}

.row .mega-links li a:hover {
	color: #000;
}

.wrapper .btn {
	color: #000000;
	font-size: 20px;
	cursor: pointer;
	display: none;
}

.wrapper .btn.close-btn {
	position: absolute;
	right: 30px;
	top: 10px;
}

@media screen and (max-width: 970px) {
	.wrapper .btn {
		display: block;
	}

	.wrapper .nav-links {
		position: fixed;
		height: 100vh;
		width: 100%;
		max-width: 350px;
		top: 0;
		left: -100%;
		background: #f8f8f8;
		display: block;
		padding: 50px 10px;
		line-height: 50px;
		overflow-y: auto;
		box-shadow: 0px 15px 15px rgba(0, 0, 0, 0.18);
		transition: all 0.3s ease;
	}

	/* custom scroll bar */
	::-webkit-scrollbar {
		width: 10px;
	}

	::-webkit-scrollbar-track {
		background: #242526;
	}

	::-webkit-scrollbar-thumb {
		background: #3A3B3C;
	}

	#menu-btn:checked~.nav-links {
		left: 0%;
	}

	#menu-btn:checked~.btn.menu-btn {
		display: none;
	}

	#close-btn:checked~.btn.menu-btn {
		display: block;
	}

	.nav-links li {
		margin: 15px 10px;
	}

	.nav-links li a {
		padding: 0 20px;
		display: block;
		font-size: 20px;
	}

	.nav-links .drop-menu {
		position: static;
		opacity: 1;
		top: 65px;
		visibility: visible;
		padding-left: 20px;
		width: 100%;
		max-height: 0px;
		overflow: hidden;
		box-shadow: none;
		transition: all 0.3s ease;
	}

	#showDrop:checked~.drop-menu,
	#showMega:checked~.mega-box {
		max-height: 100%;
	}

	#showMega1:checked~.mega-box {
		max-height: 100%;
	}

	#showMega2:checked~.mega-box {
		max-height: 100%;
	}

	.nav-links .desktop-item {
		display: none;
	}

	.nav-links .mobile-item {
		display: block;
		color: #000000;
		font-size: 20px;
		font-weight: 500;
		padding-left: 20px;
		cursor: pointer;
		border-radius: 5px;
		transition: all 0.3s ease;
	}

	.nav-links .mobile-item:hover {
		background: #3A3B3C;
	}

	.drop-menu li {
		margin: 0;
	}

	.drop-menu li a {
		border-radius: 5px;
		font-size: 18px;
	}

	.mega-box {
		position: static;
		top: 65px;
		opacity: 1;
		visibility: visible;
		padding: 0 20px;
		max-height: 0px;
		overflow: hidden;
		transition: all 0.3s ease;
	}

	.mega-box .content {
		box-shadow: none;
		flex-direction: column;
		/* padding: 20px 20px 0 20px; */
	}

	.mega-box .content .row {
		width: 100%;
		margin-bottom: 15px;
		border-top: 1px solid rgba(255, 255, 255, 0.08);
	}

	.mega-box .content .row:nth-child(1),
	.mega-box .content .row:nth-child(2) {
		border-top: 0px;
	}

	.content .row .mega-links {
		border-left: 0px;
		padding-left: 15px;
	}

	.row .mega-links li {
		margin: 0;
	}

	.content .row header {
		font-size: 19px;
	}
}

nav input {
	display: none;
}

.body-text {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	text-align: center;
	padding: 0 30px;
}

.body-text div {
	font-size: 45px;
	font-weight: 600;
}

details {
	display: block;
	width: 300px;
	margin: 10px 0;
}

summary {
	display: block;
	background: #FDC830;
	/* fallback for old browsers */
	background: -webkit-linear-gradient(to right, #F37335, #FDC830);
	/* Chrome 10-25, Safari 5.1-6 */
	background: linear-gradient(to bottom, #F37335, #FDC830);
	color: white;
	border-radius: 5px;
	padding: 5px;
	cursor: pointer;
	font-weight: bold;
}

summary::-webkit-details-marker {
	color: #FF0000;
	background: #FFFFFF;
}

details[open] summary::-webkit-details-marker {
	color: #0000FF;
	background: #00FFFF;
}

summary::-webkit-details-marker {
	display: none
}

summary:after {
	content: "+";
	color: #FFFFFF;
	float: left;
	font-size: 1.5em;
	font-weight: bold;
	margin: -5px 5px 0 0;
	padding: 0;
	text-align: center;
	width: 20px;
}

details[open] summary:after {
	content: "-";
	color: #FFFFFF
}

.co-1 {
	display: none;
}

.id-1 {
	display: none;
}

/* -------------- New Header -----------------------*/

.WE {
	padding: 0PX;
}

.more-about-us {
	padding: 20px 50px;
}

/*-------         hover images container        ------*/



.content-world {
	position: relative;
	width: 90%;
	max-width: 400px;
	margin: auto;
	overflow: hidden;
	border-radius: 10px;
}

.content-world:hover {
	box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
	transition: 1s;
}

.content-world .content-overlay {
	background: rgba(0, 0, 0, 0.7);
	position: absolute;
	height: 100%;
	width: 100%;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
	opacity: 0;
	-webkit-transition: all 0.4s ease-in-out 0s;
	-moz-transition: all 0.4s ease-in-out 0s;
	transition: all 0.4s ease-in-out 0s;
}

.content-world:hover .content-overlay {
	opacity: 1;
}

.content-image {
	width: 100%;
}

.content-details {
	position: absolute;
	text-align: center;
	padding-left: 1em;
	padding-right: 1em;
	width: 100%;
	top: 50%;
	left: 50%;
	opacity: 0;
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	-webkit-transition: all 0.3s ease-in-out 0s;
	-moz-transition: all 0.3s ease-in-out 0s;
	transition: all 0.3s ease-in-out 0s;
}

.content-world:hover .content-details {
	top: 50%;
	left: 50%;
	opacity: 1;
}

.content-details h4 {
	color: #fff;
	font-weight: 500;
	letter-spacing: 0.15em;
	margin-bottom: 0.5em;
	text-transform: uppercase;
}

.content-details p {
	color: #fff;
	font-size: 0.8em;
}

.fadeIn-bottom {
	top: 80%;
}

/*---------   our work processs     ------*/
.prt-processbox-wrapper .prt-box-image .proces-img {
	border: 0;
	border-radius: 24rem;
	transition: all .4s ease-in-out;
}

.prt-processbox-wrapper:hover .prt-box-image .proces-img {
	border: 0;
	border-radius: 5rem;
	transition: .5s all;
}

.prt-processbox-wrapper:hover .prt-box-title-h3 {
	transition: all .6s ease;
	background-image: linear-gradient(to right, #40db5c 0%, #FD4326 50%, #40db5c 100%) !important;
	background-color: transparent;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.prt-hovertext1 {
	transition: all .3s ease;
}

.process-num {
	font-family: var(--base-headingfont);
	color: #000;
	font-size: 20px;
	line-height: 30px;
	font-weight: 600;
	background-color: transparent;
	height: unset;
	width: unset;
	border-radius: 0;
	text-align: left;
	position: relative;
	left: 0;
	margin-left: 0;
	bottom: 3px;
}

/*---------   Responsive topbar     ------*/


@media only screen and (max-width:576px) {
	.top-wrapper ul li {
		display: inline-block;
		color: #8d8d8d;
		padding: 0 0 0 10px;
	}
}

@media only screen and (max-width:300px) {
	.top-wrapper ul li {
		display: inline-block;
		color: #8d8d8d;
		padding: 0 0 0 0px;
	}
}

@media only screen and (max-width:480px) {
	.top-wrapper ul li {
		display: inline-block;
		color: #8d8d8d;
		padding: 0 0 0 0px;
	}
}

/*---------   parallax  ----------*/
.parallax {
	/* The image used */
	background-image: url("../images/banner/banner-13.png");

	/* Set a specific height */
	min-height: 300px;

	/* Create the parallax scrolling effect */
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.connect-us {
	padding-top: 80px !important;
}

.fa-heart {
	color: red;
	font-size: 15px;
	font-weight: 800;
}

.product-txt:hover {
	color: #000 !important;
	transition: 1.5s;
}

.connect-us-button:hover {
	border: 1px solid green;
	background: transparent;
	color: #fff;

}

.btn-industry-read-more {
	width: 40%;
	font-size: 12px;
}

.btn-industry-read-more:hover {
	border: 1px solid green;
	background: transparent;
	color: #000 !important;
	font-weight: 600;
}

.parallax-contact-us {
	background-image: url("../images/banner/banner-6.png");
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-career {
	background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url(../images/banner/career-vid.gif);
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-industry {
	background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url(../images/parallax/industries.png);
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-service {
	background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url(../images/parallax/services.png);
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-products {
	background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url(../images/parallax/products.png);
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-industry-banking {
	background-image: url("../images/parallax/banking-finance.png");
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-industry-pharma {
	background-image: url("../images/parallax/pharma-healthcare.png");
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-industry-agriculture {
	background-image: url("../images/parallax/agriculture.png");
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-industry-goverment {
	background-image: url("../images/parallax/goverment.png");
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-industry-education {
	background-image: url("../images/parallax/education.png");
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-service-enterprise {
	background-image: url("../images/parallax/enterprise.png");
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-service-design {
	background-image: url("../images/parallax/design.png");
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-service-application-security {
	background-image: url("../images/parallax/app-security.png");
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-service-training {
	background-image: url("../images/parallax/training.png");
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.parallax-connect {
	background-image: url("../images/inner-banner6.jpg");
	min-height: 400px;
	background-attachment: fixed;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
}

.contact-us-p-text {
	margin-top: 200px;
	font-weight: 900;
	font-size: 22px;
}

.service-head-text {
	margin-top: 150px;
	font-weight: 900;
	font-size: 22px;
}

.banking-and-finance-p-text {
	margin-top: 125px;
	font-weight: 700;
	font-size: 22px;
}

.product-box {
	box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
	height: 100% !important;
	border-radius: 10px !important;
}


.products-info {
	font-size: 11px;
}


/*-------Continue Typing ---------*/
.typing-container {
	width: 100%;
	text-align: center;
	display: flex;
	justify-content: center;
	color: #ffffff;
	font-size: 2rem;
	font-weight: 800;
}

.typing-text {
	font-weight: 900;
	color: #ffffff;
}

.typing-text::after {
	content: "|";
	animation: blink 1s step-end infinite;
}

@keyframes blink {

	0%,
	100% {
		opacity: 1;
	}

	50% {
		opacity: 0;
	}
}

@media screen and (max-width: 768px) {
	.typing-container {
		font-size: 1.5rem;
	}
}

/* Progress bar */

.progress-bar-container {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 5px;
	background-color: #25272b;
	z-index: 9999;
}

.progress-bar {
	height: 100%;
	background: rgb(4, 106, 56);
	background: linear-gradient(90deg, rgba(4, 106, 56, 1) 0%, rgba(227, 71, 38, 1) 100%);
	transition: all 0.3s ease;
}

.progress-circle-container {
	position: fixed;
	bottom: 20px;
	right: 20px;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.progress-circle {
	width: 80px;
	height: 80px;
}

.progress-circle-bar {
	fill: none;
	stroke: black;
	stroke-width: 5;
	stroke-dasharray: 283;
	stroke-dashoffset: 0;
	transform-origin: center;
	transition: stroke-dashoffset 0.3s ease;
}

.progress-background {
	fill: transparent;
	stroke: #ededed;
	stroke-width: 5;
	stroke-dasharray: none;
}
.video-pandamic{
	box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}




  /* Container to hold images */
  .image-gallery {
	display: flex; /* Align images in a row */
	justify-content: center; /* Center the images horizontally */
	gap: 20px; /* Space between images */
	padding: 20px;
  }

  /* Style for each image */
  .image-gallery img {
	width: 300px;  /* Set a fixed width for the images */
	height: 300px; /* Set a fixed height for the images */
	border-radius: 10px; /* Round the corners of the images */
	box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Add subtle box shadow for a smooth effect */
	object-fit: cover; /* Ensure images maintain their aspect ratio */
	cursor: pointer; /* Change cursor to indicate clickability */
  }

  /* Lightbox background */
  .lightbox {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.8);
	justify-content: center;
	align-items: center;
	z-index: 9999;
	cursor: pointer; /* Cursor pointer to indicate it can be clicked to close */
  }

  /* Lightbox image styling */
  .lightbox img {
	max-width: 90%;
	max-height: 90%;
	border-radius: 10px;
  }

  /* Responsive styling */
  @media (max-width: 768px) {
	.image-gallery img {
	  width: 150px;
	  height: 150px;
	}
  }

  @media (max-width: 480px) {
	.image-gallery img {
	  width: 120px;
	  height: 120px;
	}
  }